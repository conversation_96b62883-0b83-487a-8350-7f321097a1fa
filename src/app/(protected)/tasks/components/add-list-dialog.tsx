"use client";

import { useState } from "react";
import { useUser } from "@stackframe/stack";
import { useAddListMutation } from "@/lib/queries";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { ListColorPicker } from "@/components/ui/list-color-picker";
import { List } from "@/lib/db";
import { ChevronLeft, Search } from "lucide-react";
import { SPACE_ICONS, renderSpaceIcon } from "@/lib/space-icons";

interface AddListDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onListAdded: (newList: List) => void;
  spaceId?: string;
}

export function AddListDialog({
  open,
  onOpenChange,
  onListAdded,
  spaceId,
}: AddListDialogProps) {
  const user = useUser();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [color, setColor] = useState<string | null>(null);
  const [selectedIcon, setSelectedIcon] = useState<string>("clipboard");
  const [iconSearch, setIconSearch] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  // TanStack Query mutation
  const addListMutation = useAddListMutation(user?.id || "", spaceId);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim()) {
      setError("List name is required");
      return;
    }

    if (!user) {
      setError("You must be logged in to create a list");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      const result = await addListMutation.mutateAsync({
        name: name.trim(),
        description: description.trim() || null,
        color: color,
        icon: selectedIcon,
        spaceId: spaceId,
      });

      if (result) {
        // Reset form and close dialog
        setName("");
        setDescription("");
        setColor(null);
        setSelectedIcon("clipboard");
        setIconSearch("");
        onOpenChange(false);
        onListAdded(result);
      } else {
        setError("Failed to create list. Please try again.");
      }
    } catch (err) {
      console.error("Error creating list:", err);
      setError("An error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Reset form state when dialog is closed
    setName("");
    setDescription("");
    setColor(null);
    setSelectedIcon("clipboard");
    setIconSearch("");
    setError("");
    onOpenChange(false);
  };

  return (
    <MobileDialog open={open} onOpenChange={handleClose}>
      <MobileDialogContent className="sm:max-w-[640px]" fullHeight>
        <VisuallyHidden asChild>
          <MobileDialogTitle>New List</MobileDialogTitle>
        </VisuallyHidden>
        <MobileDialogHeader className="flex items-start justify-start px-4 pt-4 pb-0 md:px-6 md:pt-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-6 w-6" />
            <span className="sr-only">Back</span>
          </Button>
        </MobileDialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4 px-4 md:px-6 md:gap-6">
            <div className="grid gap-2">
              <Input
                id="name"
                type="search"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="List name"
                autoComplete="off"
                spellCheck="false"
                disabled={isSubmitting}
                maxLength={120}
              />
            </div>

            <div className="grid gap-2">
              <RichTextEditor
                id="description"
                value={description}
                onChange={setDescription}
                placeholder="Add details about this list (optional)"
                maxLength={5000}
              />
            </div>

            <div className="grid gap-2">
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium">Color</span>
                <ListColorPicker
                  value={color}
                  onChange={setColor}
                  disabled={isSubmitting}
                />
              </div>
            </div>

            {/* Icon Selection - mirrors space selector, 3 rows max (h-36) */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Icon</span>
                <div className="text-2xl">{renderSpaceIcon(selectedIcon, "h-6 w-6")}</div>
              </div>

              {/* Icon Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search icons..."
                  value={iconSearch}
                  onChange={(e) => setIconSearch(e.target.value)}
                  className="pl-10"
                  autoComplete="off"
                  spellCheck="false"
                />
              </div>

              {/* Icon Grid */}
              <div className="h-36 overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
                <div className="grid grid-cols-8 gap-2">
                  {(iconSearch ? SPACE_ICONS.filter(icon => icon.toLowerCase().includes(iconSearch.toLowerCase())) : SPACE_ICONS).map((icon) => (
                    <button
                      key={icon}
                      type="button"
                      onClick={() => setSelectedIcon(icon)}
                      className={`h-10 w-10 rounded-lg border transition-colors flex items-center justify-center ${
                        selectedIcon === icon
                          ? "bg-primary/10 border-primary/20"
                          : "bg-card border-border hover:bg-muted/50"
                      }`}
                    >
                      {renderSpaceIcon(icon, "h-5 w-5")}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {error && (
              <p className="text-sm font-medium text-destructive">{error}</p>
            )}
          </div>

          <div className="flex justify-end px-4 pb-4 md:px-6 md:pb-6">
            <button
              type="submit"
              disabled={isSubmitting}
              className="text-sm font-semibold text-muted-foreground hover:text-foreground disabled:text-muted-foreground/50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? "Creating..." : "Create"}
            </button>
          </div>
        </form>
      </MobileDialogContent>
    </MobileDialog>
  );
}
